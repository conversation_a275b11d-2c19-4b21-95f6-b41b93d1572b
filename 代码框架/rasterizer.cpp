// clang-format off
//
// Created by goksu on 4/6/19.
//

#include <algorithm>
#include <vector>
#include "rasterizer.hpp"
#include <opencv2/opencv.hpp>
#include <math.h>


rst::pos_buf_id rst::rasterizer::load_positions(const std::vector<Eigen::Vector3f> &positions)
{
    auto id = get_next_id();
    pos_buf.emplace(id, positions);

    return {id};
}

rst::ind_buf_id rst::rasterizer::load_indices(const std::vector<Eigen::Vector3i> &indices)
{
    auto id = get_next_id();
    ind_buf.emplace(id, indices);

    return {id};
}

rst::col_buf_id rst::rasterizer::load_colors(const std::vector<Eigen::Vector3f> &cols)
{
    auto id = get_next_id();
    col_buf.emplace(id, cols);

    return {id};
}

auto to_vec4(const Eigen::Vector3f& v3, float w = 1.0f)
{
    return Vector4f(v3.x(), v3.y(), v3.z(), w);
}


static bool insideTriangle(int x, int y, const Vector3f* _v)
{   
    // TODO : Implement this function to check if the point (x, y) is inside the triangle represented by _v[0], _v[1], _v[2]
    bool is_inside = true;

    Vector3f p[3];
    p[0] = _v[0] - Vector3f(x, y, 0);
    p[1] = _v[1] - Vector3f(x, y, 0);
    p[2] = _v[2] - Vector3f(x, y, 0);

    Vector3f edge[3];
    edge[0] = p[1] - p[0];
    edge[1] = p[2] - p[1];
    edge[2] = p[0] - p[2];

    Vector3f cross_product[3];
    cross_product[0] = p[0].cross(edge[0]);
    cross_product[1] = p[1].cross(edge[1]);
    cross_product[2] = p[2].cross(edge[2]);

    for (int i = 0; i < 3; i++) {
        if (cross_product[i].z() < 0) {
            is_inside = false;
            break;
        }
    }

    return is_inside;
    
}

static std::tuple<float, float, float> computeBarycentric2D(float x, float y, const Vector3f* v)
{
    float c1 = (x*(v[1].y() - v[2].y()) + (v[2].x() - v[1].x())*y + v[1].x()*v[2].y() - v[2].x()*v[1].y()) / (v[0].x()*(v[1].y() - v[2].y()) + (v[2].x() - v[1].x())*v[0].y() + v[1].x()*v[2].y() - v[2].x()*v[1].y());
    float c2 = (x*(v[2].y() - v[0].y()) + (v[0].x() - v[2].x())*y + v[2].x()*v[0].y() - v[0].x()*v[2].y()) / (v[1].x()*(v[2].y() - v[0].y()) + (v[0].x() - v[2].x())*v[1].y() + v[2].x()*v[0].y() - v[0].x()*v[2].y());
    float c3 = (x*(v[0].y() - v[1].y()) + (v[1].x() - v[0].x())*y + v[0].x()*v[1].y() - v[1].x()*v[0].y()) / (v[2].x()*(v[0].y() - v[1].y()) + (v[1].x() - v[0].x())*v[2].y() + v[0].x()*v[1].y() - v[1].x()*v[0].y());
    return {c1,c2,c3};
}

void rst::rasterizer::draw(pos_buf_id pos_buffer, ind_buf_id ind_buffer, col_buf_id col_buffer, Primitive type)
{
    auto& buf = pos_buf[pos_buffer.pos_id];
    auto& ind = ind_buf[ind_buffer.ind_id];
    auto& col = col_buf[col_buffer.col_id];

    float f1 = (50 - 0.1) / 2.0;
    float f2 = (50 + 0.1) / 2.0;

    Eigen::Matrix4f mvp = projection * view * model;
    for (auto& i : ind)
    {
        Triangle t;
        Eigen::Vector4f v[] = {
                mvp * to_vec4(buf[i[0]], 1.0f),
                mvp * to_vec4(buf[i[1]], 1.0f),
                mvp * to_vec4(buf[i[2]], 1.0f)
        };
        //Homogeneous division
        for (auto& vec : v) {
            vec /= vec.w();
        }
        //Viewport transformation
        for (auto & vert : v)
        {
            vert.x() = 0.5*width*(vert.x()+1.0);
            vert.y() = 0.5*height*(vert.y()+1.0);
            vert.z() = vert.z() * f1 + f2;
        }

        for (int i = 0; i < 3; ++i)
        {
            t.setVertex(i, v[i].head<3>());
            t.setVertex(i, v[i].head<3>());
            t.setVertex(i, v[i].head<3>());
        }

        auto col_x = col[i[0]];
        auto col_y = col[i[1]];
        auto col_z = col[i[2]];

        t.setColor(0, col_x[0], col_x[1], col_x[2]);
        t.setColor(1, col_y[0], col_y[1], col_y[2]);
        t.setColor(2, col_z[0], col_z[1], col_z[2]);

        rasterize_triangle(t);
    }
}

//Screen space rasterization
void rst::rasterizer::rasterize_triangle(const Triangle& t) {
    auto v = t.toVector4();

    // TODO : Find out the bounding box of current triangle.
    // iterate through the pixel and find if the current pixel is inside the triangle

    // If so, use the following code to get the interpolated z value.
    //auto[alpha, beta, gamma] = computeBarycentric2D(x, y, t.v);
    //float w_reciprocal = 1.0/(alpha / v[0].w() + beta / v[1].w() + gamma / v[2].w());
    //float z_interpolated = alpha * v[0].z() / v[0].w() + beta * v[1].z() / v[1].w() + gamma * v[2].z() / v[2].w();
    //z_interpolated *= w_reciprocal;

    // TODO : set the current pixel (use the set_pixel function) to the color of the triangle (use getColor function) if it should be painted.


    // Find out the bounding box of current triangle
    auto x_min = std::min(v[0].x(), std::min(v[1].x(), v[2].x()));
    auto x_max = std::max(v[0].x(), std::max(v[1].x(), v[2].x()));
    auto y_min = std::min(v[0].y(), std::min(v[1].y(), v[2].y()));
    auto y_max = std::max(v[0].y(), std::max(v[1].y(), v[2].y()));

    // Super-sampling: 2x2 samples per pixel
    // Sample positions within a pixel: (0.25, 0.25), (0.75, 0.25), (0.25, 0.75), (0.75, 0.75)
    float sample_offsets[4][2] = {
        {0.25f, 0.25f},
        {0.75f, 0.25f},
        {0.25f, 0.75f},
        {0.75f, 0.75f}
    };

    for (int x = x_min; x < x_max; x++) {
        for (int y = y_min; y < y_max; y++) {
            int pixel_index = get_index(x, y);

            // Test each of the 4 samples within this pixel
            for (int sample_id = 0; sample_id < 4; sample_id++) {
                float sample_x = x + sample_offsets[sample_id][0];
                float sample_y = y + sample_offsets[sample_id][1];

                if (insideTriangle(sample_x, sample_y, t.v)) {
                    auto [alpha, beta, gamma] = computeBarycentric2D(sample_x, sample_y, t.v);
                    float w_reciprocal = 1.0 / (alpha / v[0].w() + beta / v[1].w() + gamma / v[2].w());
                    float z_interpolated = alpha * v[0].z() / v[0].w() + beta * v[1].z() / v[1].w() + gamma * v[2].z() / v[2].w();
                    z_interpolated *= w_reciprocal;

                    // Depth test for this sample
                    if (z_interpolated < sample_buf[pixel_index][sample_id].depth) {
                        sample_buf[pixel_index][sample_id].depth = z_interpolated;
                        sample_buf[pixel_index][sample_id].color = t.getColor();
                        sample_buf[pixel_index][sample_id].valid = true;
                    }
                }
            }

            // After processing all samples for this pixel, compute the final color
            // by averaging all valid samples
            Eigen::Vector3f final_color(0, 0, 0);
            int valid_samples = 0;

            for (int sample_id = 0; sample_id < 4; sample_id++) {
                if (sample_buf[pixel_index][sample_id].valid) {
                    final_color += sample_buf[pixel_index][sample_id].color;
                    valid_samples++;
                }
            }

            if (valid_samples > 0) {
                final_color /= valid_samples;
                frame_buf[pixel_index] = final_color;
            }
        }
    }
}

// Alternative version without super-sampling for comparison
void rst::rasterizer::rasterize_triangle_no_supersampling(const Triangle& t) {
    auto v = t.toVector4();

    auto x_min = std::min(v[0].x(), std::min(v[1].x(), v[2].x()));
    auto x_max = std::max(v[0].x(), std::max(v[1].x(), v[2].x()));
    auto y_min = std::min(v[0].y(), std::min(v[1].y(), v[2].y()));
    auto y_max = std::max(v[0].y(), std::max(v[1].y(), v[2].y()));

    for (int x = x_min; x < x_max; x++) {
        for (int y = y_min; y < y_max; y++) {
            if (insideTriangle(x+0.5f, y+0.5f, t.v)) {
                auto [alpha, beta, gamma] = computeBarycentric2D(x+0.5f, y+0.5f, t.v);
                float w_reciprocal = 1.0 / (alpha / v[0].w() + beta / v[1].w() + gamma / v[2].w());
                float z_interpolated = alpha * v[0].z() / v[0].w() + beta * v[1].z() / v[1].w() + gamma * v[2].z() / v[2].w();
                z_interpolated *= w_reciprocal;
                if (z_interpolated < depth_buf[get_index(x, y)]) {
                    depth_buf[get_index(x, y)] = z_interpolated;
                    set_pixel(Eigen::Vector3f(x, y, z_interpolated), t.getColor());
                }
            }
        }
    }
}

void rst::rasterizer::set_model(const Eigen::Matrix4f& m)
{
    model = m;
}

void rst::rasterizer::set_view(const Eigen::Matrix4f& v)
{
    view = v;
}

void rst::rasterizer::set_projection(const Eigen::Matrix4f& p)
{
    projection = p;
}

void rst::rasterizer::clear(rst::Buffers buff)
{
    if ((buff & rst::Buffers::Color) == rst::Buffers::Color)
    {
        std::fill(frame_buf.begin(), frame_buf.end(), Eigen::Vector3f{0, 0, 0});
    }
    if ((buff & rst::Buffers::Depth) == rst::Buffers::Depth)
    {
        std::fill(depth_buf.begin(), depth_buf.end(), std::numeric_limits<float>::infinity());
        // Clear all samples in the sample buffer
        for (auto& pixel_samples : sample_buf) {
            for (auto& sample : pixel_samples) {
                sample.depth = std::numeric_limits<float>::infinity();
                sample.color = Eigen::Vector3f{0, 0, 0};
                sample.valid = false;
            }
        }
    }
}

rst::rasterizer::rasterizer(int w, int h) : width(w), height(h)
{
    frame_buf.resize(w * h);
    depth_buf.resize(w * h);
    sample_buf.resize(w * h); // Each pixel has 4 samples
}

int rst::rasterizer::get_index(int x, int y)
{
    return (height-1-y)*width + x;
}

void rst::rasterizer::set_pixel(const Eigen::Vector3f& point, const Eigen::Vector3f& color)
{
    //old index: auto ind = point.y() + point.x() * width;
    auto ind = (height-1-point.y())*width + point.x();
    frame_buf[ind] = color;
    // Note: With super-sampling, final colors are computed directly in rasterize_triangle
    // This method is kept for compatibility but may not be used in super-sampling mode
}

// clang-format on